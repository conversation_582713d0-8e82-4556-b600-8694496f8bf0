# 《修仙职业技术学院》

## 1. 核心玩法机制与游戏驱动

《修仙职业技术学院》是以由开罗软件（Kairosoft）开发的《冒险迷宫村》（Dungeon Village）为灵感来源的城镇建设模拟游戏，其核心玩法围绕着在一个修仙世界中，通过发展学院来吸引并培养强大的冒险者，从而共同抵御怪物的侵袭，并最终将村庄建设成五星级的繁荣都市 。游戏巧妙地将城镇经营与角色扮演（RPG）元素相结合，玩家不仅要扮演规划者的角色，负责村庄的布局、设施建设和资源调配，还要扮演幕后支持者的角色，通过赠送礼物、举办活动和颁发勋章等方式，间接地培养和引导冒险者们的成长。这种双重身份的设计构成了游戏独特的魅力，使得玩家在宏观的战略布局与微观的角色养成之间不断切换，体验到一个动态且充满活力的游戏世界。

### 1.1 游戏目标与核心循环

#### 1.1.1 最终目标：提升村落星级至5星

游戏的最终目标非常明确，即通过持续的发展和建设，将玩家所管理的村庄从一个默默无闻的边陲小镇，逐步提升其知名度和繁荣度，最终获得最高的**五星级评价** 。这个星级评定系统是贯穿游戏始终的核心驱动力，它综合反映了村庄在多个维度上的发展水平，包括但不限于村庄的人气、设施的完善程度、冒险者的整体实力以及抵御怪物入侵的能力。为了达成这一目标，玩家需要不断地优化村庄的布局，建造和升级各类功能建筑，吸引更多的冒险者前来定居，并确保他们能够有效地完成各种任务和挑战。星级提升的过程并非一蹴而就，而是需要玩家在游戏的不同阶段，根据村庄的实际情况和冒险者的成长需求，制定出合理的发展策略，并持续地进行投入和调整。

#### 1.1.2 核心循环：建设 -> 吸引冒险者 -> 培养 -> 战斗 -> 获取资源 -> 再建设

《冒险迷宫村》的核心玩法遵循一个清晰且高度自洽的循环机制。这个循环始于 **“建设”** ，玩家利用初始资源建造基础的生活和战斗设施，如旅馆、武器店等，为冒险者提供必要的服务 。随着设施的完善，村庄的吸引力会逐渐增加，从而 **“吸引冒险者”** 前来。这些冒险者会自动地在村庄周围进行战斗，通过战斗他们会获得经验值和金币，同时也会消耗在村庄中的各种服务。玩家的任务就是通过 **“培养”** 这些冒险者，例如赠送礼物提升他们的满意度和努力度，或者为他们举办活动来增强属性，从而提升他们的战斗效率。更强大的冒险者能够挑战更强大的怪物，完成更困难的任务，从而 **“获取资源”** ，包括金币、道具和稀有的城镇点数。这些新获取的资源又可以被用于 **“再建设”** ，即建造更高级的建筑、升级现有设施或解锁新的功能，从而开启新一轮的循环。这个循环环环相扣，每一步都为下一步的发展奠定基础，形成了一个正向的反馈回路，驱动着玩家不断地投入时间和精力，推动村庄向着五星级的目标稳步前进。

### 1.2 游戏驱动机制

#### 1.2.1 时间系统：年度、季度循环与事件刷新

时间系统是《冒险迷宫村》中一个基础且重要的驱动机制，它为游戏世界带来了动态的变化和节奏感。游戏中的时间以 **“年”和“季度”** 为单位流逝，这种设计不仅模拟了真实世界的时间流逝，也为各种游戏内事件的触发提供了框架 。例如，每年都会有定期的城镇评价，根据玩家的发展情况给予反馈和奖励 。此外，玩家在每个季度内可以举办的活动次数是有限的，这要求玩家必须进行合理的规划和决策 。时间的流逝还会影响到怪物的刷新强度和任务的更新，随着游戏内年份的增加，玩家将面临越来越强大的挑战。这种基于时间的驱动机制，使得游戏进程具有了一种自然的推进感，玩家无法通过无限刷取资源来快速通关，而是需要耐心地经营和等待，逐步发展壮大自己的城镇。同时，年度结算时的税收和勋章颁发，也为玩家提供了周期性的目标和奖励，进一步增强了游戏的粘性。

#### 1.2.2 人气系统：影响冒险者来访、事件触发与勋章获取

人气系统是《冒险迷宫村》中连接城镇发展与冒险者招募的关键枢纽，是推动游戏进程的核心驱动力之一。人气值会随着城镇的发展而不断提升，例如建造新的建筑、成功举办活动、完成任务等都会增加人气 。每当人气值累积满100点时，就会触发一个 **“好事件”** ，通常是新的冒险者慕名而来，或者解锁新的游戏内容 。人气的高低直接决定了来访冒险者的数量和质量，高人气能够吸引更多强大的冒险者，为城镇的防御和发展提供更强大的助力。更重要的是，人气是获取 **“勋章”** 这一稀有资源的唯一途径 。勋章是培养顶尖冒险者、解锁高级职业的必需品，因此，如何有效地提升人气，成为了玩家在游戏中后期需要重点考虑的战略问题。人气系统的设计，巧妙地将城镇的建设与冒险者的培养联系在了一起，玩家必须通过不断发展和宣传自己的城镇，才能获得更强大的冒险者，进而挑战更高难度的内容，形成了一个良性的发展循环。

#### 1.2.3 刷怪机制：村口怪物无限刷新，强度随时间提升

刷怪机制是《冒险迷宫村》中为冒险者提供持续战斗经验和资源的基础系统。在游戏地图的特定区域，怪物会不断地刷新出现，为冒险者提供了源源不断的战斗机会 。这些被击败的怪物会掉落金币、道具，并为冒险者提供经验值，是冒险者成长的重要途径。更重要的是，这个刷怪机制具有**动态难度调整**的特点，随着时间的推移和游戏内年份的增加，刷新出的怪物会变得越来越强大 。这种设计确保了游戏的挑战性会随着玩家的发展而同步提升，避免了玩家因过度强大而感到无聊。同时，这也对玩家的城镇建设和冒险者培养提出了持续的要求，玩家必须不断地提升冒险者的实力，才能应对越来越强的怪物。此外，定居在城镇的冒险者每年缴纳的税金，也与他们击败的怪物数量直接挂钩，这进一步激励玩家积极地派遣冒险者进行战斗，将刷怪行为与城镇的经济收入紧密地结合在了一起。

#### 1.2.4 任务刷新：完成任务后，新任务会不断出现

任务系统是《冒险迷宫村》中为玩家提供明确目标和丰厚奖励的核心驱动机制。游戏中的任务种类繁多，包括**探索迷宫、讨伐特定怪物群以及挑战强大的BOSS**等 。每当玩家完成一个任务后，系统会自动刷新出新的、更具挑战性的任务，为玩家提供了持续不断的游戏内容和追求目标 。这些任务不仅是获取稀有道具和装备的主要途径，也是提升城镇人气和获取大量村落点数的重要方式 。任务的难度和奖励会随着游戏进程而逐步提升，引导玩家不断地去挑战自己的极限。玩家可以有选择性地接受任务，并可以花费一定的金币来招募更多的冒险者参与，这为任务的完成提供了策略性的选择。任务系统的设计，为玩家提供了一个清晰的短期和中期目标，使得游戏进程充满了方向感和成就感，有效地避免了玩家在自由发展过程中可能产生的迷茫感。

#### 1.2.5 商店刷新：商店道具列表会定期更新

商店刷新机制是《冒险迷宫村》中保障玩家能够持续获取新道具和装备的重要系统。游戏中的商店，包括武器店、防具店和饰品店，其出售的道具列表会随着时间的推移而**定期更新** 。这意味着玩家不能一次性购买到所有想要的装备，而是需要持续地关注商店的动态，以便在第一时间购买到新解锁的强力装备。这种机制增加了游戏的随机性和期待感，玩家每次查看商店时都可能会有新的发现。同时，这也与游戏的探索和战斗系统紧密相连，许多高级装备需要通过完成任务或在迷宫中开启宝箱来 **“解锁”** ，解锁后才会出现在商店的货架上 。因此，玩家必须积极地参与战斗和探索，才能不断丰富商店的商品种类，为自己的冒险者提供更强大的武装。商店刷新机制的设计，有效地将游戏的各个系统串联了起来，促进了玩家在游戏中的持续活跃和探索。

## 2. 冒险者系统与培养

冒险者是《冒险迷宫村》的核心资产，他们既是村落繁荣的守护者，也是玩家投入资源进行培养的主要对象。整个冒险者系统设计得相当复杂且富有深度，涵盖了从获取、行为模式到属性成长和培养策略的方方面面。玩家需要深入理解这些机制，才能有效地打造出一支强大的冒险者队伍，从而应对日益严峻的挑战。

### 2.1 冒险者获取与行为

#### 2.1.1 获取方式：随游戏进程自动来访

在《冒险迷宫村》中，冒险者的获取方式是完全被动的，玩家无法通过主动招募来增加人手。新的冒险者会随着游戏进程的推进，被村落不断提升的人气所吸引，自动前来访问 。游戏内总共有**25名**独特的冒险者可供玩家发现和培养 。每一位新冒险者的到来，通常都伴随着村落人气值的提升或特定事件的触发 。这种设计将冒险者的获取与村落的整体发展水平紧密地联系在一起，激励玩家不断优化村落的建设和服务，以吸引更多、更优秀的冒险者。当一位新的冒险者首次到访时，他们通常只是作为访客，在村中短暂停留，使用一些设施后便可能离开。如何将他们转化为长期定居的居民，是玩家需要面对的第一个挑战。

#### 2.1.2 行为模式：自动战斗、回城消费、使用设施

冒险者在游戏中的行为模式是高度自主的，玩家无法直接控制他们的具体行动。当冒险者在村落附近时，他们会自动寻找并攻击出现的怪物 。战斗结束后，他们会返回村落，进行一系列的消费和休整活动。这些活动包括：在旅馆中恢复生命值（HP），在武器店、防具店购买或更换装备，在各种餐饮和娱乐设施中消费以提升满意度和属性 。冒险者的行为逻辑旨在最大化其战斗效率，例如，当生命值较低时，他们会优先寻找旅馆进行恢复。这种自动化的行为模式，将玩家的角色定位从“微操指挥官”转变为“宏观战略家”，玩家的主要任务是为冒险者们创造一个功能完善、服务周到的环境，让他们能够自发地进行战斗和成长。

#### 2.1.3 定居机制：满意度达标后可申请定居，提升效率

将访客冒险者转化为定居者是提升村落战斗力和运营效率的关键一步。当一位冒险者对村落的**满意度（Satisfaction）** 达到特定阈值后（通常是30点），他们就会向玩家提出申请，希望能够在村中定居 。此时，玩家需要花费一定的资源（如1000G）为他们建造一座房屋 。一旦定居，冒险者将永久留在村中，不再离开。定居带来的好处是多方面的：首先，定居的冒险者每年会根据其击败怪物的数量向村落缴纳**税收**，成为村落重要的收入来源之一 。其次，定居的冒险者使用村内设施的频率更高，能够更稳定地提升自身属性和满意度。最后，定居的冒险者更容易响应玩家的任务号召，参与迷宫探索和大型讨伐活动，从而提升任务的成功率和效率。因此，积极提升冒险者的满意度，并为他们提供住所，是村落发展壮大的核心策略。

### 2.2 冒险者属性详解

冒险者的属性系统是游戏中最复杂的部分之一，它由多个维度的属性构成，共同决定了一个冒险者的综合实力。这些属性可以分为基础战斗属性、基础成长属性和关键培养属性三大类。

#### 2.2.1 基础战斗属性：生命值(HP)、攻击力、防御力、魔法力(Spirit)

这些属性直接决定了冒险者在战斗中的表现。
*   **生命值 (HP)** ：代表冒险者的生存能力。当HP降至0时，冒险者会陷入“倒地”状态，暂时失去战斗能力 。HP可以通过装备防具和饰品来提升。
*   **攻击力 (Attack)** ：决定冒险者对怪物造成的物理伤害。高攻击力意味着更快的清怪速度。攻击力主要由武器决定 。
*   **防御力 (Defense)** ：影响冒险者受到物理伤害时所减免的伤害量。高防御力能显著提升冒险者在战斗中的持久性。防御力主要由防具和饰品提供 。
*   **魔法力 (Spirit)** ：决定冒险者使用魔法攻击时造成的伤害，以及治愈魔法恢复的生命值。对于法师等魔法职业而言，这是核心属性。魔法力可以通过特定武器和饰品来提升 。

#### 2.2.2 基础成长属性：体力(Health)、力量(Strength)、灵巧(Dexterity)、结实(Toughness)、魔力(Magic)、运气(Luck)

这些基础属性是战斗属性的基石，它们以一定的比例影响战斗属性，并决定了冒险者的成长潜力。
*   **体力 (Health)** ：主要影响生命值（HP）的上限 。
*   **力量 (Strength)** ：主要影响攻击力（Attack） 。
*   **灵巧 (Dexterity)** ：影响冒险者在战斗中的攻击次数。灵巧越高，进行多次攻击的概率就越大，是提升输出的重要属性 。
*   **结实 (Toughness)** ：主要影响防御力（Defense） 。
*   **魔力 (Magic)** ：主要影响魔法力（Spirit） 。
*   **运气 (Luck)** ：影响冒险者在开启宝箱时获得的物品质量。高运气意味着有更高几率获得稀有和强力的装备 。

#### 2.2.3 关键培养属性：满意度(Satisfaction)与努力度(Work)

这两个属性是玩家可以通过外部手段直接干预和培养的核心指标，它们对冒险者的成长至关重要。
*   **满意度 (Satisfaction)** ：衡量冒险者对村落的喜爱程度。满意度主要通过使用高品质的设施或接受玩家赠送的礼物来提升 。当满意度达到特定水平时，冒险者才会申请在村庄定居，这是将他们转化为稳定税收来源的关键一步 。
*   **努力度 (Work)** ：代表了冒险者的勤奋程度。努力度的提升方式与满意度类似，主要通过赠送礼物和颁发勋章 。努力度是一个极其重要的属性，因为它直接影响冒险者的基础属性。具体来说，**每提升10点努力度，冒险者的所有基础属性（体力、力量、灵巧等）就会获得10%的加成，最高可提升至100%（即翻倍）** 。此外，努力度越高的冒险者，在战斗中触发 **“斗气”（Aura）** 状态的几率也越高 。

### 2.3 冒险者培养途径

为了打造一支强大的冒险者队伍，玩家需要综合运用多种培养手段。这些途径各有侧重，消耗的资源也不同，需要玩家根据自身的发展阶段和战略目标进行权衡。

#### 2.3.1 赠送礼物：使用道具或装备提升满意度与努力度

赠送礼物是最直接、最灵活的培养方式。玩家可以将背包中的消耗品道具或装备直接赠送给冒险者，以提升他们的满意度和努力度 。不同的礼物提升的数值不同，通常越稀有、越昂贵的物品提升效果越好。例如，赠送一把强力的武器，不仅能大幅提升冒险者的战斗属性，还能显著增加其满意度和努力度 。这种培养方式的优点在于可以精确地针对单个冒险者进行强化，但缺点是成本较高，尤其是高级装备的价格不菲。

#### 2.3.2 举办活动：消耗村落点数，提升全体冒险者属性与满意度

举办活动是一种高效的群体性培养方式。玩家可以消耗 **“村落点数”（Town Points）** 来举办各种活动，如“海上训练”、“魔法理论”等 。这些活动能够一次性地提升所有在村冒险者的特定属性（如攻击力、防御力、魔法力等）和满意度。虽然举办活动需要消耗相对稀缺的村落点数，但其覆盖面广、效率高的特点，使其成为快速提升整体战斗力的重要手段。特别是在游戏中期，当玩家拥有一定数量的冒险者后，举办活动的性价比会非常高。

#### 2.3.3 颁发勋章：每年颁发，大幅提升满意度、努力度，是转职高级职业的必要条件

勋章是游戏中最珍贵的培养资源。每年年底，根据村落的人气表现，系统会奖励玩家一定数量的勋章 。玩家可以将这些勋章颁发给心仪的冒险者。每个勋章可以为冒险者提供**10点满意度和10点努力度**的永久加成 。更重要的是，**勋章是解锁高级职业的“钥匙”** 。许多强力职业，如“国王”、“开罗君”等，都对冒险者拥有的勋章数量有严格要求 。因此，如何合理分配有限的勋章，是玩家在游戏后期需要深思熟虑的战略决策。

#### 2.3.4 职业转职：通过消耗村落点数改变职业，大幅修正属性

职业转职是彻底改变冒险者成长方向和战斗定位的终极培养手段。玩家可以消耗 **“村落点数”** 为冒险者更换职业 。不同的职业对基础属性的修正比例完全不同，例如，战士职业的力量和体力成长较高，而法师职业的魔力和精神成长较高。当一个职业被练到满级后，该职业还会被解锁，供其他冒险者转职使用 。转职的魅力在于，冒险者的基础属性会保留下来，这意味着玩家可以通过不断转职，让冒险者继承多个职业的优秀成长，最终打造出属性全面、能力超群的“六边形战士”。

## 3. 职业体系 (Jobs)

《冒险迷宫村》的职业体系是游戏深度和策略性的核心体现之一。它不仅为冒险者提供了多样化的成长路径，还通过复杂的解锁和转职机制，为玩家的长期培养规划提供了丰富的目标。职业对冒险者属性的影响是决定性的，一个合适的职业选择往往能让冒险者的战斗力产生质的飞跃。

### 3.1 职业解锁与转职机制

#### 3.1.1 解锁方式：特定职业的冒险者达到满级后解锁

游戏中的大部分职业并非一开始就全部开放，而是需要通过特定的方式来解锁。最主要的解锁方式是 **“传承”** 。当一位拥有特定职业的冒险者（无论是初始职业还是已转职的职业）被培养至该职业的等级上限（通常是10级或30级）后，这个职业就会被“解锁”，从此之后，其他所有符合条件的冒险者都可以选择转职为该职业 。这种设计鼓励玩家去培养每一位到访的冒险者，即使他们的初始属性并不出众，因为他们可能携带着解锁强力职业的关键。

#### 3.1.2 转职消耗：消耗村落点数

为冒险者进行职业转换需要消耗一种名为 **“村落点数”** 的稀缺资源 。村落点数主要通过完成各种任务和击败怪物来获取，是游戏中除了金币之外最重要的货币之一 。每次为冒险者进行转职，都需要支付一定数量的村落点数，转职的目标职业越高级，所需的点数通常也越多。这种消耗机制为玩家的转职决策增加了策略性，玩家不能随意地为所有冒险者频繁转职，而需要根据自己的资源状况和团队需求，进行合理的规划。

#### 3.1.3 转职限制：部分高级职业有性别和勋章数量要求

除了消耗村落点数外，部分高级和特殊职业的转职还设有额外的门槛。其中，最重要的限制条件就是 **“勋章”** 数量。例如，游戏中最强大的终极职业 **“开罗君”和“开罗子”** ，要求冒险者必须拥有**至少5个勋章**才能进行转职 。勋章作为每年年底根据村落人气发放的稀有奖励，其获取速度非常缓慢，这使得能够转职为终极职业的冒险者凤毛麟角，成为玩家后期追求的顶级目标。此外，某些职业可能还存在性别限制，只有特定性别的冒险者才能转职。

### 3.2 职业分类与特点

游戏中的职业种类繁多，根据其解锁难度和属性加成特点，可以大致分为初级、中级和高级三个层次。

| 职业层级 | 代表职业 | 特点与定位 |
| :--- | :--- | :--- |
| **初级职业** | 冒险家、农民、战士 | 游戏初期即可接触，升级所需经验少，是解锁高级职业的基础。战士是前期可靠的物理输出 。 |
| **中级职业** | 骑士、法师、忍者、弓箭手 | 在特定属性上有显著加成，战斗定位明确。骑士是坚实的前排坦克，法师是强大的魔法输出，忍者以高灵巧和高暴击著称 。 |
| **高级职业** | 国王、公主、开罗君/开罗姬 | 游戏后期的顶级追求，拥有无与伦比的属性加成和强大的能力。开罗君/开罗姬是终极职业，需要5个勋章才能转职 。 |

<br>

*Table 1: 职业层级分类与特点*

<br>

#### 3.2.1 初级职业：冒险家、农民、战士

这些是游戏初期冒险者自带的职业，也是最容易解锁和升级的。
*   **冒险家 (Adventurer)** ：作为最基础的职业，各项属性较为均衡，没有明显的短板，但也没有突出的优势。其升级所需经验较少，是游戏初期快速培养、解锁新职业的理想选择 。
*   **农民 (Farmer)** ：与冒险家类似，属于基础职业，升级经验需求低。虽然在战斗方面表现平平，但可能在某些与资源生产相关的建筑上提供小幅加成 。
*   **战士 (Warrior)** ：相比前两者，战士在力量和体力方面有更明显的加成，是前期可靠的物理输出和肉盾。其升级经验需求同样较低，是组建初期战斗队伍的核心力量 。

#### 3.2.2 中级职业：骑士、法师、忍者等

随着游戏的进行，玩家会逐渐解锁这些职业。它们在特定属性上拥有显著的加成，使得冒险者的战斗定位更加明确。
*   **骑士 (Knight)** ：相比战士，骑士在防御力（结实）和生命值（体力）上拥有更高的加成，是团队中最坚实的前排坦克。
*   **法师 (Mage)** ：法师是魔法输出的核心，其魔力（Magic）和魔法力（Spirit）属性得到极大强化。虽然物理防御和生命值较低，但其强大的范围魔法伤害是清场的利器 。
*   **忍者 (Ninja)** ：忍者以其极高的灵巧（Dexterity）属性著称，这意味着他们拥有更多的攻击次数，能够在短时间内造成爆发性的物理伤害。

#### 3.2.3 高级职业：国王、开罗君/开罗姬（终极职业）

这些职业是游戏后期的顶级追求，拥有无与伦比的属性加成和强大的能力。
*   **国王 (King)** ：作为高级职业的代表，国王在各项属性上都有着非常均衡且高额的加成，是全能型战士的典范。
*   **开罗君/开罗姬 (Kairobot/Kairobotte)** ：这是游戏中最强大的终极职业，其解锁条件极为苛刻，需要冒险者拥有**5个勋章** 。一旦转职成功，冒险者的所有属性都会得到爆炸性的提升，成为能够横扫一切敌人的存在。获得并培养出一名开罗君，是玩家通关游戏、达成全成就的标志性事件。

### 3.3 职业对属性的影响

#### 3.3.1 属性修正：不同职业对各项战斗属性有大幅加成

职业对冒险者属性的影响是游戏中最核心的机制之一。每个职业都有一套独特的**属性成长修正系数**，这决定了冒险者在升级时，各项基础属性（体力、力量、灵巧等）的增长幅度。例如，一个战士在升级时，其力量属性的增长会远高于法师；反之，法师的魔力属性增长则会远超战士。这种巨大的差异使得职业选择直接决定了冒险者未来的战斗风格和定位。一个优秀的职业搭配，能够让整个冒险者团队的战斗力产生1+1>2的效果。

#### 3.3.2 职业加成：部分职业对村落建设能力有小幅加成

除了直接影响战斗属性外，部分职业还会对村落的建设和发展产生间接的加成效果。例如，农民职业可能会提高农田的产量，而商人职业则可能增加商店的收入。虽然这些加成效果通常比较微小，但在长期的游戏过程中，它们也能为村落的繁荣做出一定的贡献。这种设计进一步丰富了职业的策略价值，使得玩家在选择培养对象时，不仅要考虑其战斗潜力，还要兼顾其对村落整体发展的贡献。

## 4. 战斗系统

《冒险迷宫村》的战斗系统是游戏玩法的重要组成部分，它将模拟经营的成果（强大的冒险者）与RPG元素（战斗与成长）紧密地结合在一起。尽管战斗过程是自动进行的，但其背后的机制、策略深度和奖励体系都设计得相当完善，为玩家提供了持续的挑战和乐趣。

### 4.1 战斗模式与机制

#### 4.1.1 战斗方式：全自动战斗，玩家无法直接操控

游戏采用了**全自动的回合制战斗模式**。一旦战斗开始，玩家无法对冒险者的行动进行任何干预，包括选择攻击目标、使用技能或道具等 。冒险者们会根据自己的AI逻辑自主进行战斗，例如优先攻击威胁最大的敌人，或在生命值较低时选择防御。这种设计将玩家的角色从“战术指挥官”转变为“战略策划者”，玩家的主要任务是在战斗前通过合理的队伍配置、装备搭配和属性培养来确保胜利。战斗的观赏性较强，玩家可以实时看到冒险者与怪物之间的攻防、技能释放和伤害数字，从而获得战斗的反馈。

#### 4.1.2 战斗触发：村口遭遇、任务讨伐、探索迷宫

游戏中的战斗主要通过三种方式触发：
*   **村口遭遇**：这是最常见、最频繁的战斗形式。在村落入口附近，会不断刷新出各种怪物，冒险者们会自动上前迎战 。这种战斗是冒险者获取经验值和金币的主要来源。
*   **任务讨伐**：玩家可以在任务板上接受各种讨伐任务，这些任务通常要求玩家击败特定区域的一群怪物或一个强大的BOSS 。接受任务需要消耗一定的金币，但奖励也更为丰厚。
*   **探索迷宫**：随着游戏进程，玩家会解锁各种迷宫。探索迷宫本质上是一系列连续的战斗，玩家需要派遣一支冒险者队伍深入其中，击败沿途的怪物，最终找到宝藏或击败迷宫深处的BOSS 。迷宫探索是获取稀有装备和道具的主要途径。

#### 4.1.3 战斗状态：倒地（HP为0）、复活（被同伴救回或自动恢复）

在战斗中，当冒险者的生命值（HP）降至0时，他们会进入 **“倒地”（Unconscious）** 状态，暂时无法行动 。此时，冒险者无法继续攻击，但并不会立即死亡。他们有两种复活方式：一种是等待一段时间，生命值会自动缓慢恢复，恢复满后便会重新站起来投入战斗；另一种是被同行的其他冒险者“救回”，即被同伴带回城镇的旅馆进行休养，这种方式可以大大加快恢复速度 。此外，玩家也可以使用特定的道具或魔法来复活倒地的冒险者。这种战斗状态的设计，增加了战斗的真实感和策略性。玩家需要考虑团队的续航能力，确保队伍中有能够治疗或复活的职业，以应对长时间的战斗。

### 4.2 战斗属性与策略

#### 4.2.1 攻击类型：近战、远程、魔法

游戏中的攻击方式主要分为三种：
*   **近战攻击**：由战士、骑士等前排职业使用，通常拥有较高的单次伤害。
*   **远程攻击**：由弓箭手等职业使用，可以在安全距离对敌人造成伤害。
*   **魔法攻击**：由法师等职业使用，通常具有范围伤害效果，能够同时攻击多个敌人，是对付大量弱小敌人的利器 。

#### 4.2.2 属性相克：魔法属性（火、雷、冰）的运用

游戏中的魔法攻击具有属性之分，主要包括**火、雷、冰**三种 。不同的怪物可能对不同的魔法属性有抗性或弱点。例如，一个冰属性的怪物可能对火系魔法非常脆弱，但对冰系魔法则有很高的抗性。因此，在挑战强大的敌人或迷宫BOSS时，了解其属性弱点，并派遣相应属性的法师出战，是制定有效战斗策略的关键。这种属性相克机制增加了战斗的深度和策略性。

#### 4.2.3 特殊状态：“斗气”的触发与效果（提升攻防与攻击次数）

**“斗气”（Aura）** 是战斗中一种非常强大的增益状态。当冒险者的 **“努力度”（Work）** 属性较高时，就有更高的几率在战斗中触发斗气 。进入斗气状态后，冒险者的攻击力、防御力和攻击次数都会得到显著提升 。这个机制将冒险者的培养属性（努力度）与战斗表现直接挂钩，极大地激励了玩家去提升冒险者的努力度。一次关键的斗气触发，往往能够扭转战局，帮助玩家战胜强大的敌人。

### 4.3 战斗奖励

#### 4.3.1 经验值：提升冒险者等级

战斗胜利后，参战的冒险者会获得经验值。当经验值累积到一定程度，冒险者就会升级，其各项基础属性也会随之增长。这是冒险者成长最基本、最稳定的途径。

#### 4.3.2 金币：基础货币

击败怪物是获取**金币（Gold）** 的主要方式之一。金币是游戏中最基础的货币，用于购买建筑、道具、装备等几乎所有消费行为 。

#### 4.3.3 宝箱：红色宝箱（消耗品）、蓝色宝箱（装备）

在战斗结束后，有一定几率会掉落宝箱。宝箱分为两种：
*   **红色宝箱**：通常包含各种消耗性道具，如恢复药水、临时属性提升药剂等。
*   **蓝色宝箱**：通常包含装备，如武器、防具或饰品。从宝箱中获得的装备是**解锁商店新商品**的重要途径，也是玩家获取高级装备的主要来源之一 。

## 5. 经济系统

《冒险迷宫村》的经济系统是支撑整个游戏世界运转的基石，它通过一套精巧的货币和资源循环机制，将村庄的建设、冒险者的培养、战斗的奖励以及道具的流通紧密地联系在一起。玩家作为村庄的管理者，必须精通这套经济系统的运作规律，才能在有限的资源下，做出最优的决策，推动村庄走向繁荣。游戏中的经济体系并非单一维度的金币积累，而是包含了多种不同用途和获取方式的货币与资源，它们共同构成了一个复杂而富有深度的经济生态。

### 5.1 货币体系

| 货币类型 | 主要用途 | 获取途径 | 稀有度 |
| :--- | :--- | :--- | :--- |
| **金币 (Gold)** | 购买道具、装备、建筑，开启任务 | 战斗掉落、任务奖励、税收 | 常见 |
| **村落点数 (Town Points)** | 举办活动、职业转职、解锁建筑 | 完成任务、击败BOSS、村庄月收入 | 稀缺 |
| **勋章 (Medals)** | 提升冒险者满意度和努力度，解锁高级职业 | 人气值累积奖励（每100点） | 非常稀有 |
| **魔法点数 (Essences)** | 在炼金锅中解锁特殊道具、装备和建筑 | 通过炼金锅消耗道具转化 | 特殊 |

<br>

*Table 2: 游戏内货币体系概览*

<br>

#### 5.1.1 金币：最常用货币，用于购买道具、装备、建筑

**金币（Gold）** 是游戏中最基础、最常用的货币单位 。它的获取途径相对广泛，主要通过冒险者击败怪物、完成任务以及村庄的税收来获得。金币的消耗也无处不在，无论是建造新的设施、升级现有建筑，还是在商店中购买道具和装备，都需要花费大量的金币。可以说，金币是维持村庄日常运营和发展的血液，其流动贯穿了游戏的始终。在游戏初期，金币往往是制约发展速度的主要瓶颈，玩家需要通过精打细算，合理规划每一笔开销，才能确保村庄的稳步发展。

#### 5.1.2 村落点数：稀缺货币，用于举办活动、转职、解锁建筑

**村落点数（Town Points）** 是一种比金币更为稀缺和重要的战略资源 。它的获取途径相对有限，主要通过完成高难度的任务、击败强大的BOSS以及村庄每月的固定收入来获得。村落点数的用途也更为关键，它可以用来举办能够提升全体冒险者属性和满意度的活动，可以为冒险者进行职业转换，还可以解锁一些功能强大但价格昂贵的特殊建筑。由于村落点数的稀缺性，如何分配这些点数，成为了游戏中一项至关重要的战略决策。玩家需要在提升单个冒险者实力、增强整个村庄的福利以及拓展村庄的建设规模之间做出艰难的权衡。

#### 5.1.3 勋章：最稀有资源，用于颁发给冒险者，解锁高级职业

**勋章（Medals）** 是游戏中最稀有、最珍贵的资源。它的主要用途有两个：一是作为礼物颁发给冒险者，可以一次性大幅提升他们的满意度和努力度，是培养核心主力的最快方式 ；二是作为解锁某些顶级职业（如国王、公主、开罗君）的必要条件 。勋章的获取途径非常单一，主要是通过提升村庄的人气值，每当人气达到200的整数倍时，玩家可以从冒险者公会获得一枚勋章 。因此，勋章的获取速度直接决定了玩家培养顶级战力的速度，其分配策略至关重要。

#### 5.1.4 魔法点数：通过炼金锅消耗道具获得，用于解锁新内容

**魔法点数（Essences）** 是通过“炼金锅”（Cauldron）系统获得的一种特殊资源。玩家可以将多余的道具投入炼金锅，将其转化为火、雷、冰、暗四种属性的魔法点数 。这些魔法点数可以用来解锁一些特殊的装备、道具和建筑，是获取稀有物品的重要途径。魔法点数的引入，为游戏中多余的道具提供了一个有效的回收渠道，也增加了资源管理的维度。

### 5.2 资源获取途径

#### 5.2.1 战斗掉落：击败怪物获取金币与道具

战斗是游戏中最基础、最持续的资源获取方式。冒险者在野外击败怪物后，会获得金币作为直接的战利品 。同时，战斗胜利后还有几率掉落各种道具，包括消耗品和装备。这些掉落的道具是玩家获取资源的重要补充，可以直接使用，也可以作为礼物赠送给冒险者，或者投入炼金锅进行合成。因此，保证冒险者们能够持续高效地进行战斗，是维持村庄经济稳定的基础。

#### 5.2.2 任务奖励：完成任务获取金币、道具与村落点数

相比于普通的野外战斗，完成任务能够获得更为丰厚的奖励。游戏中的任务种类繁多，包括讨伐怪物群、探索迷宫、挑战BOSS等。完成这些任务后，玩家不仅可以获得大量的金币和稀有的道具，还有机会获得珍贵的**村落点数** 。因此，积极地派遣冒险者去完成各种任务，是快速积累财富和战略资源的关键。特别是那些奖励村落点数的任务，更是玩家在游戏中期和后期需要优先完成的目标。

#### 5.2.3 税收：定居的冒险者每年缴纳，与击败怪物数量挂钩

税收是村庄一项稳定且重要的收入来源。当冒险者在村庄定居后，他们每年都需要向村庄缴纳一次税金 。税金的数额并非固定，而是与该冒险者在过去一年中击败的怪物数量直接挂钩。击败的怪物越多，贡献的税收也就越多。这个机制巧妙地将冒险者的战斗行为与村庄的经济收益联系在了一起，激励玩家不仅要吸引冒险者定居，还要努力提升他们的战斗能力，让他们为村庄创造更多的价值。一个拥有众多强大定居冒险者的村庄，其税收收入将会非常可观，从而为村庄的进一步发展提供坚实的经济保障。

#### 5.2.4 炼金系统：通过炼金锅将多余道具转化为魔法点数

炼金锅系统为玩家提供了一个将“垃圾”变“宝”的途径。玩家可以将背包中多余的、低级的道具投入炼金锅，将其转化为魔法点数 。这些魔法点数可以用来解锁一些通过常规途径无法获得的稀有装备和建筑，从而变废为宝，实现资源的最大化利用。

### 5.3 资源消耗途径

#### 5.3.1 建筑建设与升级

这是金币最主要的消耗途径之一。玩家需要花费大量金币来建造新的设施，以完善村庄的功能和提升人气。此外，对现有建筑进行升级和强化，也需要消耗金币和特定的道具 。

#### 5.3.2 购买道具与装备

为冒险者购买更强大的装备和实用的道具，是提升团队战斗力的直接手段。商店中的高级装备价格不菲，需要玩家积累足够的金币才能购买 。

#### 5.3.3 举办活动与职业转职

这两项是村落点数的主要消耗途径。举办活动可以快速提升冒险者的整体实力，而职业转职则是打造顶级战力的必经之路。玩家需要在两者之间做出权衡，合理分配有限的村落点数 。

## 6. 村落建设系统

村落建设是《冒险迷宫村》的核心玩法之一，玩家作为村庄的规划者和建设者，需要通过合理的布局和持续的投资，将一个简陋的营地逐步建设成一个设施完善、繁荣兴旺的冒险者之都。游戏中的建筑系统非常丰富，涵盖了住宿、商业、娱乐等多个方面，每种建筑都有其独特的功能和作用。如何根据村庄的发展阶段和冒险者的需求，选择性地建造和升级建筑，是玩家需要不断思考和优化的核心问题。

### 6.1 建筑类型与功能

| 建筑类别 | 代表建筑 | 核心功能 |
| :--- | :--- | :--- |
| **住宿设施** | 旅馆 (Inn) | 恢复冒险者HP，复活倒地冒险者  |
| **商业设施** | 武器店、防具店、饰品店 | 供冒险者购买装备，提升战斗力，为村庄带来收入  |
| **餐饮设施** | 包子铺、烤肉店 | 提供食物，提升冒险者满意度  |
| **特殊设施** | 战斗学校、魔法研究所、城堡 | 提升冒险者基础属性（力量、魔力等），或大幅提升村庄人气  |

<br>

*Table 3: 主要建筑类型与功能*

<br>

#### 6.1.1 住宿设施：旅馆（Inn），恢复冒险者HP

**旅馆（Inn）** 是村庄中最基础也是最重要的设施之一，其核心功能是为冒险者提供休息和恢复的场所。当冒险者在战斗中受伤，HP下降时，他们可以选择进入旅馆进行休整，从而将HP完全恢复 。此外，当冒险者在战斗中不幸昏迷倒地后，他们也会被其他同伴或系统机制运送到旅馆进行救治，在旅馆中的恢复速度远快于在野外自行恢复 。因此，在村庄中建造至少一个旅馆，是确保冒险者能够持续作战、维持村庄战斗力的基本保障。一个位置便利、等级较高的旅馆，能够极大地提升冒险者的战斗效率和满意度，是村庄规划中必须优先考虑的建设项目。

#### 6.1.2 商业设施：武器店、防具店、饰品店，供冒险者购买装备

商业设施是村庄经济循环和冒险者实力提升的核心环节，主要包括**武器店、防具店和饰品店**。这些商店为冒险者提供了购买和更新装备的渠道 。冒险者会根据自己的经济能力和需求，自主地前往这些商店消费，购买更强大的武器来提升攻击力，购买更坚固的防具来增强防御力，或是购买有特殊效果的饰品来辅助战斗。玩家通过建设并升级这些商店，不仅可以为冒险者提供更好的装备选择，还能从中赚取金币，促进村庄的经济发展。当冒险者通过探索迷宫等方式解锁了新的装备后，这些新装备便会出现在商店的货架上，供所有冒险者购买，从而形成一个“探索-解锁-消费”的良性循环 。

#### 6.1.3 餐饮设施：提供食物，提升冒险者满意度

餐饮设施，如包子铺、烤肉店等，主要作用是提升冒险者的**满意度**。一个吃饱喝足的冒险者，自然会对村庄有更好的印象，从而提升定居的可能性。部分高级餐饮设施还可能提供额外的属性加成 。这些设施是提升冒险者满意度的有效途径，对于吸引他们定居至关重要。

#### 6.1.4 特殊设施：提升冒险者属性或满意度的其他建筑

除了基础的住宿和商业设施外，游戏中还存在大量功能各异的特殊设施。这些设施可以为冒险者提供各种服务，例如提升他们的基础属性（如力量、灵巧等），或者增加他们的满意度 。例如，建造一个**战斗学校**，可以让冒险者在战斗之余进行训练，从而永久性地提升某项属性；而建造一个**酒馆或花园**，则可以让冒险者放松身心，大幅提升他们的满意度。这些特殊设施的存在，为村庄的建设提供了更多的策略选择，玩家可以根据自己的培养方向，有针对性地建造这些设施，从而打造出独具特色的村庄。

### 6.2 建筑升级与强化

#### 6.2.1 升级方式：使用道具对建筑进行强化

建筑并非一成不变，玩家可以通过使用特定的道具来对建筑进行强化和升级。这些道具可以通过战斗掉落、完成任务或在商店中购买获得。将道具用于建筑后，可以提升该建筑的“品质”或“效果” 。例如，对旅馆使用恢复类道具，可以提升其恢复HP的效率；对商店使用商业类道具，可以增加其商品的吸引力，从而吸引更多冒险者消费。这种通过消耗道具来升级建筑的方式，将道具系统与建设系统紧密地结合在了一起，使得玩家获取的每一份资源都有了更广泛的用途。

#### 6.2.2 升级效果：提升建筑的效果、便利度和吸引力

对建筑进行升级，可以带来多方面的积极效果。首先，最直接的效果是提升建筑的核心功能，例如旅馆的恢复量、商店的销售额等。其次，升级还能提升建筑的 **“便利度”和“吸引力”** ，这会影响冒险者选择使用该设施的频率。一个高品质的建筑，不仅能更好地满足冒险者的需求，还能显著提升他们的满意度 。当建筑的品质提升到一定程度时，甚至可能触发特殊事件，或者成为提升村庄人气的重要因素 。因此，持续地对关键建筑进行投资升级，是提升村庄整体服务水平和吸引力的重要手段。

#### 6.2.3 升级策略：优先升级旅馆和关键商业设施

在资源有限的情况下，玩家需要制定合理的升级策略。通常建议**优先升级旅馆**，以保证冒险者的续航能力。其次，应重点升级**武器店和防具店**，因为强大的装备是提升冒险者战斗力的最直接途径。对于能够提升关键属性的特殊设施，如剑术道场和魔法研究所，也应尽早建设和升级。

## 7. 道具与装备系统

道具与装备系统是《冒险迷宫村》中连接战斗、建设和角色培养等多个核心玩法的关键枢纽。玩家通过战斗和任务获取的各种物品，不仅是提升冒险者实力的直接手段，也是推动村庄发展、解锁新内容的重要资源。游戏中的物品种类繁多，功能各异，从一次性的消耗品到永久性的装备，再到用于合成的神秘材料，共同构成了一个丰富而有趣的物品生态。

### 7.1 道具分类

#### 7.1.1 消耗品：使用后提升冒险者或建筑的属性

消耗品是游戏中一类重要的道具，其特点是在使用后便会消失，但能为玩家带来即时的、显著的效果。这些道具的用途非常广泛，主要可以分为两大类：一类是直接作用于冒险者，能够临时或永久地提升他们的某项属性，例如增加攻击力、防御力或HP上限的药水；另一类则是作用于建筑设施，能够提升该设施的效果、便利度或吸引力，从而间接提升冒险者的满意度和使用效率 。消耗品可以通过战斗掉落、任务奖励或商店购买等多种途径获得，是玩家在培养冒险者和建设村庄过程中不可或缺的辅助资源。

#### 7.1.2 装备品：武器、防具、饰品，提升冒险者战斗属性

装备品是提升冒险者战斗力的核心道具，主要分为**武器、防具和饰品**三大类。
*   **武器**：主要提升冒险者的攻击力或魔法力，是决定其输出能力的关键。
*   **防具**：主要提升冒险者的防御力和HP，是保障其生存能力的基础。
*   **饰品**：则提供各种多样化的附加效果，如提升某项基础成长属性、增加暴击率、或是提供特殊抗性等。

冒险者可以装备一件武器、一件防具和一件饰品，通过合理的装备搭配，玩家可以针对性地强化冒险者的长处或弥补其短板，打造出符合特定战术需求的战斗单位 。

### 7.2 道具获取与解锁

#### 7.2.1 解锁方式：村民入住、战斗开宝箱、炼金锅炼化

在《冒险迷宫村》中，许多强力的道具和装备并非一开始就能在商店中买到，而是需要通过特定的方式“解锁”。主要的解锁途径有三种：
1.  **村民入住**：当特定的冒险者定居在村庄后，他们可能会带来独特的道具或装备，从而解锁这些物品在商店中的购买权。
2.  **战斗开宝箱**：如前所述，从**蓝色宝箱**中首次获得某件装备后，该装备便会立即解锁，出现在商店的货架上 。这是解锁新装备最主要的方式。
3.  **炼金锅炼化**：通过炼金锅系统，玩家可以消耗特定的材料来炼化出全新的、甚至是独一无二的道具和装备，这些通过炼金获得的物品也会被解锁 。

#### 7.2.2 获取方式：花费金币购买、战斗掉落、炼金锅炼化

解锁后的道具和装备，可以通过以下方式获取：
*   **花费金币购买**：在商店中直接购买，是获取装备最稳定的方式。
*   **战斗掉落**：击败怪物或开启宝箱有几率直接获得。
*   **炼金锅炼化**：通过炼金术直接制作出来。

### 7.3 炼金锅 (Cauldron)

#### 7.3.1 功能：消耗道具，转化为魔法点数

**炼金锅（Cauldron）** 是游戏中一个非常独特且功能强大的系统。其核心功能是将玩家背包中多余的或不再需要的消耗品道具投入其中，通过炼化过程将其转化为一种名为 **“魔法点数”** 的特殊资源 。这个功能为那些看似无用的道具提供了一个有效的回收渠道，避免了资源的浪费。玩家可以通过炼金锅，将战斗中大量获得的低级药水或材料，转化为更有价值的魔法点数，从而实现资源的优化配置。

#### 7.3.2 解锁内容：通过消耗魔法点数解锁特殊道具、装备和建筑

通过炼金锅获得的“魔法点数”并非终点，而是解锁更多高级内容的钥匙。玩家可以消耗积累的魔法点数，来解锁一系列通过常规途径无法获得的**特殊道具、强力装备，甚至是功能独特的稀有建筑** 。这些通过炼金解锁的内容，往往具有远超普通物品的性能和效果，是玩家在游戏后期打造顶级村庄和冒险者团队的关键。因此，炼金锅系统形成了一个“消耗道具 -> 获得点数 -> 解锁高级内容”的完整闭环，为游戏增添了极大的深度和可玩性，鼓励玩家不断尝试和探索不同的炼金配方，以发掘隐藏的宝藏。

## 8. 任务与事件系统

### 8.1 任务系统 (Quests)

#### 8.1.1 任务类型：迷宫探索、怪物群讨伐、BOSS讨伐

游戏中的任务系统为冒险者提供了明确的目标和丰富的挑战内容。任务板上会定期刷新出不同类型的任务，主要包括：
*   **迷宫探索**：要求冒险者组队进入特定的迷宫进行探索，在探索过程中会遭遇多轮战斗，并最终面对迷宫深处的强大敌人。
*   **怪物群讨伐**：要求冒险者前往指定区域，消灭一定数量的特定怪物。
*   **BOSS讨伐**：挑战强大的单体BOSS，通常需要玩家派遣最强的队伍，并制定周密的战术。

#### 8.1.2 任务机制：消耗金币开启，可额外招募冒险者参与

接受任务通常需要消耗一定数量的**金币**，任务的难度越高，消耗的金币也越多。在派遣队伍时，玩家可以选择让哪些冒险者参与。如果人手不足，玩家还可以花费额外的金币，临时招募一些不在村的冒险者前来助阵，这为完成高难度任务提供了更多的策略选择。

#### 8.1.3 任务奖励：经验、金币、道具、装备，是解锁新装备的主要途径

完成任务后，玩家可以获得丰厚的奖励，包括大量的**经验值、金币、稀有道具和装备**。其中，从任务奖励的蓝色宝箱中获得的装备，是**解锁商店新商品**的主要途径。因此，积极地完成各种任务，是提升村庄整体实力和丰富装备库的关键。

### 8.2 事件系统 (Events)

#### 8.2.1 事件类型：随机事件、年度活动、特殊挑战

游戏中的事件系统为村庄的发展增添了更多的变数和乐趣。事件主要分为以下几类：
*   **随机事件**：在村庄发展过程中随机触发，例如新的冒险者来访、发现稀有道具等。
*   **年度活动**：每年固定时间举办的大型活动，玩家可以消耗资源来举办，以获得全体冒险者的属性提升。
*   **特殊挑战**：满足特定条件后触发的限时挑战，完成后可以获得独特的奖励。

#### 8.2.2 触发条件：提升人气、完成特定任务、达到特定条件

事件的触发条件多种多样，最常见的触发方式是**提升村庄的人气值**。每当人气条集满100点，就有可能触发一个“好事件” 。此外，完成某些特定的任务、达成特定的村庄星级、或者拥有特定的冒险者定居，也可能触发相应的事件。

#### 8.2.3 事件效果：提升人气、获得稀有奖励、解锁新内容

事件的效果也是多种多样的，最直接的效果是**提升村庄的人气值**。许多事件还会奖励玩家稀有的道具、装备或大量的金币。更重要的是，一些关键的事件是**解锁新建筑、新职业或新任务**的必要条件，推动着游戏剧情和内容的不断发展。