extends Node2D

## 云朵生成器组件
## 负责管理云朵的生成、移动和回收
class_name CloudGenerator

# 导出参数 - 可在编辑器中配置
@export var cloud_count: int = 0  ## 云朵数量，0为随机生成
@export var drift_vector: Vector2 = Vector2.ZERO  ## 飘动向量，ZERO为随机生成
@export var boundary: Rect2 = Rect2()  ## 边界区域，空为随机生成
@export var speed: float = 0.0  ## 移动速度，0为随机生成

# 内部变量
var active_clouds: Array[Node2D] = []  ## 当前活跃的云朵实例
var cloud_scene: PackedScene  ## 云朵场景资源
var rng: RandomNumberGenerator  ## 随机数生成器

# 常量配置
const CLOUD_SCENE_PATH = "res://src/世界/环境/云.tscn"
const DEFAULT_CLOUD_COUNT_RANGE = [5, 15]
const DEFAULT_SPEED_RANGE = [20.0, 60.0]
const DEFAULT_DRIFT_SPEED_RANGE = [30.0, 80.0]
const SPEED_VARIATION_RANGE = [0.8, 1.2]
const SPRITE_OFFSET_MAX = 5
const BOUNDARY_MARGIN = 200  ## 边界外扩边距
const SPAWN_MARGIN = 50  ## 生成位置的边距

func _ready() -> void:
	_initialize_component()

func _process(delta: float) -> void:
	_update_clouds(delta)
	_maintain_cloud_count()

## 初始化组件
func _initialize_component() -> void:
	rng = RandomNumberGenerator.new()
	rng.randomize()

	# 加载云朵场景资源
	cloud_scene = load(CLOUD_SCENE_PATH)
	if not cloud_scene:
		push_error("无法加载云朵场景: " + CLOUD_SCENE_PATH)
		return

	# 生成默认参数（如果未配置）
	_generate_default_parameters()

	# 生成初始云朵
	_spawn_initial_clouds()

## 生成默认参数
func _generate_default_parameters() -> void:
	# 如果云朵数量为0，随机生成
	if cloud_count <= 0:
		cloud_count = rng.randi_range(DEFAULT_CLOUD_COUNT_RANGE[0], DEFAULT_CLOUD_COUNT_RANGE[1])

	# 如果飘动向量为零，随机生成水平飘动
	if drift_vector == Vector2.ZERO:
		var direction = rng.randf_range(-1.0, 1.0)  # 可以向左或向右
		var speed_val = rng.randf_range(DEFAULT_DRIFT_SPEED_RANGE[0], DEFAULT_DRIFT_SPEED_RANGE[1])
		drift_vector = Vector2(direction, 0).normalized() * speed_val

	# 如果边界为空，基于视口生成
	if boundary == Rect2():
		var viewport_size = get_viewport().get_visible_rect().size
		boundary = Rect2(
			-BOUNDARY_MARGIN,
			-BOUNDARY_MARGIN,
			viewport_size.x + BOUNDARY_MARGIN * 2,
			viewport_size.y + BOUNDARY_MARGIN * 2
		)

	# 如果速度为0，随机生成
	if speed <= 0.0:
		speed = rng.randf_range(DEFAULT_SPEED_RANGE[0], DEFAULT_SPEED_RANGE[1])

## 生成初始云朵
func _spawn_initial_clouds() -> void:
	for i in range(cloud_count):
		_spawn_cloud()

## 更新所有云朵的位置
func _update_clouds(delta: float) -> void:
	for cloud in active_clouds:
		if cloud and is_instance_valid(cloud):
			# 根据飘动向量和速度更新位置
			var movement = drift_vector * speed * delta
			cloud.position += movement
		else:
			# 移除无效的云朵引用
			active_clouds.erase(cloud)

## 维持云朵数量
func _maintain_cloud_count() -> void:
	# 检查并移除超出边界的云朵
	var clouds_to_remove = []
	for cloud in active_clouds:
		if cloud and is_instance_valid(cloud):
			if _is_cloud_outside_boundary(cloud):
				clouds_to_remove.append(cloud)
		else:
			clouds_to_remove.append(cloud)

	# 移除超出边界的云朵
	for cloud in clouds_to_remove:
		_recycle_cloud(cloud)

	# 补充云朵数量
	while active_clouds.size() < cloud_count:
		_spawn_cloud()

## 生成新云朵
func _spawn_cloud() -> void:
	if not cloud_scene:
		return

	var cloud_instance = cloud_scene.instantiate()
	if not cloud_instance:
		push_error("无法实例化云朵场景")
		return

	# 设置随机的sprite_offset
	var sprite_offset = rng.randi_range(0, SPRITE_OFFSET_MAX)
	cloud_instance.sprite_offset = sprite_offset

	# 设置生成位置
	cloud_instance.position = _get_spawn_position()

	# 添加到场景树
	add_child(cloud_instance)
	active_clouds.append(cloud_instance)

## 回收云朵
func _recycle_cloud(cloud: Node2D) -> void:
	if cloud and is_instance_valid(cloud):
		active_clouds.erase(cloud)
		cloud.queue_free()

## 检查云朵是否超出边界
func _is_cloud_outside_boundary(cloud: Node2D) -> bool:
	if not cloud or not is_instance_valid(cloud):
		return true

	var cloud_pos = cloud.position
	var extended_boundary = Rect2(
		boundary.position.x - SPAWN_MARGIN,
		boundary.position.y - SPAWN_MARGIN,
		boundary.size.x + SPAWN_MARGIN * 2,
		boundary.size.y + SPAWN_MARGIN * 2
	)

	return not extended_boundary.has_point(cloud_pos)

## 计算云朵生成位置
func _get_spawn_position() -> Vector2:
	var spawn_pos = Vector2()

	# 根据飘动方向确定生成边缘
	if abs(drift_vector.x) > abs(drift_vector.y):
		# 主要水平移动
		if drift_vector.x > 0:
			# 向右飘动，从左边生成
			spawn_pos.x = boundary.position.x - SPAWN_MARGIN
		else:
			# 向左飘动，从右边生成
			spawn_pos.x = boundary.position.x + boundary.size.x + SPAWN_MARGIN

		# Y坐标在边界范围内随机
		spawn_pos.y = rng.randf_range(boundary.position.y, boundary.position.y + boundary.size.y)
	else:
		# 主要垂直移动
		if drift_vector.y > 0:
			# 向下飘动，从上边生成
			spawn_pos.y = boundary.position.y - SPAWN_MARGIN
		else:
			# 向上飘动，从下边生成
			spawn_pos.y = boundary.position.y + boundary.size.y + SPAWN_MARGIN

		# X坐标在边界范围内随机
		spawn_pos.x = rng.randf_range(boundary.position.x, boundary.position.x + boundary.size.x)

	return spawn_pos

## 获取当前活跃云朵数量
func get_active_cloud_count() -> int:
	return active_clouds.size()

## 清理所有云朵
func clear_all_clouds() -> void:
	for cloud in active_clouds:
		if cloud and is_instance_valid(cloud):
			cloud.queue_free()
	active_clouds.clear()

