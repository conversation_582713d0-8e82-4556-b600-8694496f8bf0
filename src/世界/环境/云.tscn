[gd_scene load_steps=4 format=3 uid="uid://byfbr8kly37ay"]

[ext_resource type="Texture2D" uid="uid://cryaqpcp171g7" path="res://res/美术/cloud-0002.png" id="1_7iqrw"]
[ext_resource type="Script" uid="uid://de8gtrjwi4sxg" path="res://src/世界/环境/云.gd" id="2_quqrv"]

[sub_resource type="AtlasTexture" id="AtlasTexture_pj8ev"]
atlas = ExtResource("1_7iqrw")
region = Rect2(0, 0, 256, 148)

[node name="云" type="Sprite2D"]
scale = Vector2(50, 50)
texture = SubResource("AtlasTexture_pj8ev")
script = ExtResource("2_quqrv")
