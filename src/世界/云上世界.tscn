[gd_scene load_steps=4 format=3 uid="uid://ds8obfjd2v0pa"]

[ext_resource type="Script" uid="uid://t4wu1uetxgy4" path="res://src/世界/环境/造云机.gd" id="1_nsao6"]

[sub_resource type="Gradient" id="Gradient_2y8k3"]
offsets = PackedFloat32Array(0)
colors = PackedColorArray(0.3882353, 0.60784316, 1, 1)
metadata/_snap_enabled = true

[sub_resource type="GradientTexture2D" id="GradientTexture2D_ocfg7"]
gradient = SubResource("Gradient_2y8k3")
width = 128
height = 128

[node name="Node2D" type="Node2D"]

[node name="世界" type="Node2D" parent="."]

[node name="天空" type="Sprite2D" parent="世界"]
scale = Vector2(64, 64)
texture = SubResource("GradientTexture2D_ocfg7")

[node name="云层" type="Node2D" parent="世界"]
script = ExtResource("1_nsao6")
cloud_count = 3
boundary = Rect2(0, 0, 500, 500)

[node name="生物" type="Node2D" parent="世界"]

[node name="地形" type="TileMapLayer" parent="世界"]

[node name="环境" type="TileMapLayer" parent="世界"]

[node name="建筑" type="TileMapLayer" parent="世界"]
