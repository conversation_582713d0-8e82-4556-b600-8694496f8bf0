# Cloud Generator Design Document

## Overview

The cloud generator component is a Node2D-based system that manages the automatic creation, movement, and lifecycle of cloud sprites in the 云上世界 game. The component will be designed as a self-contained system that can be easily attached to any scene node and configured through exported parameters. It will maintain a pool of cloud instances, handle their movement along configurable drift vectors, and automatically recycle clouds that move outside the defined boundaries.

## Architecture

### Component Structure

The cloud generator will be implemented as a single GDScript class `CloudGenerator` that extends Node2D. This design choice allows for easy placement in the scene tree and provides access to 2D transformation methods.

```
CloudGenerator (Node2D)
├── Configuration Parameters (exported)
├── Cloud Pool Management
├── Spawn System
├── Movement System
└── Boundary Checking System
```

### Core Systems

1. **Configuration System**: Manages exported parameters with fallback to random generation
2. **Pool Management**: Maintains active cloud instances and handles recycling
3. **Spawn System**: Creates new clouds at appropriate boundary positions
4. **Movement System**: Updates cloud positions based on drift vectors and individual speeds
5. **Boundary System**: Monitors cloud positions and triggers recycling when needed

## Components and Interfaces

### CloudGenerator Class

**Exported Parameters:**
- `cloud_count: int` - Target number of clouds to maintain (default: random 5-15)
- `drift_vector: Vector2` - Base movement direction and speed (default: random horizontal drift)
- `boundary: Rect2` - Active area for cloud movement (default: screen bounds + margin)
- `speed_multiplier: float` - Global speed scaling factor (default: random 0.5-2.0)

**Internal Properties:**
- `active_clouds: Array[Node2D]` - Currently active cloud instances
- `cloud_scene: PackedScene` - Preloaded cloud scene resource
- `spawn_timer: Timer` - Controls spawn rate when clouds need replacement

**Key Methods:**
- `_ready()` - Initialize component and spawn initial clouds
- `_process(delta)` - Update cloud positions and check boundaries
- `spawn_cloud()` - Create and configure a new cloud instance
- `recycle_cloud(cloud)` - Remove cloud and spawn replacement
- `get_spawn_position()` - Calculate appropriate spawn position based on drift direction
- `is_cloud_outside_boundary(cloud)` - Check if cloud has left the active area

### Cloud Instance Management

Each cloud instance will be managed through the following lifecycle:

1. **Creation**: Instantiate from `云.tscn` scene
2. **Configuration**: Set random `sprite_offset` and individual speed variation
3. **Positioning**: Place at calculated spawn position
4. **Movement**: Update position each frame based on drift vector and speed
5. **Boundary Check**: Monitor position relative to boundary rectangle
6. **Recycling**: Remove from scene and spawn replacement when outside boundary

## Data Models

### CloudData Structure
```gdscript
class CloudData:
    var instance: Node2D
    var speed_variation: float  # Individual speed multiplier (0.8-1.2)
    var sprite_offset: int      # Visual variation (0-5)
```

### Configuration Defaults
```gdscript
const DEFAULT_CLOUD_COUNT_RANGE = [5, 15]
const DEFAULT_SPEED_RANGE = [0.5, 2.0]
const DEFAULT_DRIFT_SPEED_RANGE = [20.0, 60.0]  # pixels per second
const SPEED_VARIATION_RANGE = [0.8, 1.2]
const SPRITE_OFFSET_MAX = 5
const BOUNDARY_MARGIN = 200  # pixels beyond screen edge
```

## Error Handling

### Resource Loading
- **Issue**: Cloud scene fails to load
- **Handling**: Log error and disable component, preventing crashes
- **Fallback**: Component becomes inactive but doesn't break the game

### Boundary Configuration
- **Issue**: Invalid boundary rectangle (negative size, zero area)
- **Handling**: Auto-generate boundary based on viewport size with margins
- **Validation**: Check boundary validity in `_ready()` and correct if needed

### Performance Considerations
- **Issue**: Too many clouds causing performance issues
- **Handling**: Implement maximum cloud count limit (50 clouds)
- **Monitoring**: Track frame time and reduce cloud count if performance degrades

### Scene Integration
- **Issue**: Component attached to wrong node type or missing dependencies
- **Handling**: Validate parent node structure and log warnings
- **Graceful Degradation**: Continue operation with reduced functionality if possible

## Testing Strategy

### Unit Testing Approach

1. **Parameter Validation Tests**
   - Test default parameter generation
   - Validate boundary rectangle calculations
   - Verify speed and count range enforcement

2. **Cloud Lifecycle Tests**
   - Test cloud spawning at correct positions
   - Verify movement calculations
   - Validate boundary detection and recycling

3. **Integration Tests**
   - Test component attachment to scene tree
   - Verify cloud scene instantiation
   - Test interaction with existing cloud system

### Performance Testing

1. **Load Testing**
   - Test with maximum cloud counts
   - Measure frame rate impact
   - Validate memory usage patterns

2. **Boundary Testing**
   - Test edge cases with extreme boundary sizes
   - Verify behavior with very small or large drift vectors
   - Test rapid parameter changes during runtime

### Visual Testing

1. **Movement Verification**
   - Verify clouds move in expected directions
   - Check for smooth, natural-looking movement
   - Validate visual variety through sprite offsets

2. **Spawn/Despawn Testing**
   - Verify clouds appear from correct edges
   - Check that cloud count remains consistent
   - Validate recycling behavior at boundaries

## Implementation Notes

### Scene Integration
The component is designed to be attached to the `世界/云层` node in `云上世界.tscn`. This placement ensures proper layering and allows the component to manage all generated clouds within the appropriate scene hierarchy.

### Performance Optimization
- Use object pooling for cloud instances to reduce garbage collection
- Implement spatial partitioning if cloud counts become very large
- Cache frequently accessed properties like viewport size and boundary calculations

### Extensibility
The design allows for future enhancements such as:
- Weather-based cloud behavior changes
- Multiple cloud types with different movement patterns
- Dynamic boundary adjustment based on camera movement
- Cloud density variations based on time of day or weather conditions